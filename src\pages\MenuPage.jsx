import React, { useState, useEffect } from 'react';
import '../styles/Menu.css';

const MenuPage = () => {
  const [menus, setMenus] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeCategory, setActiveCategory] = useState('all');
  const [categories, setCategories] = useState([]);

  useEffect(() => {
    fetch("/api/get_menu.php")
      .then((res) => res.json())
      .then((data) => {
        if (data.success) {
          setMenus(data.data);
          // Ambil kategori unik dari data menu
          const uniqueCategories = [
            { id: 'all', name: 'Se<PERSON>a Menu' },
            ...Array.from(new Set(data.data.map(item => item.kategori_id))).map(id => {
              const item = data.data.find(menu => menu.kategori_id === id);
              return { id, name: item ? item.kategori_nama || `Kategori ${id}` : `Kategori ${id}` };
            })
          ];
          setCategories(uniqueCategories);
        } else {
          setError(data.message || "Gagal mengambil data menu");
        }
      })
      .catch((err) => {
        setError("Gagal mengambil data menu: " + err.message);
      })
      .finally(() => setLoading(false));
  }, []);

  const filteredMenus = activeCategory === 'all'
    ? menus
    : menus.filter(menu => menu.kategori_id === activeCategory);

  if (loading) {
    return <div className="text-center mt-5"><span>Loading...</span></div>;
  }
  if (error) {
    return <div className="alert alert-danger mt-4">{error}</div>;
  }

  const dishes = [
    {
      id: 1,
      title: 'Nasi Goreng Spesial',
      category: 'main',
      price: 'Rp 45.000',
      image: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?ixlib=rb-4.0.3',
      description: 'Nasi goreng dengan telur, ayam, udang, dan sayuran segar',
      rating: 4.8,
      badge: 'Populer'
    },
    {
      id: 2,
      title: 'Sate Ayam',
      category: 'main',
      price: 'Rp 35.000',
      image: 'https://images.unsplash.com/photo-1529563021893-cc83c992d75d?ixlib=rb-4.0.3',
      description: 'Sate ayam dengan bumbu kacang khas Indonesia',
      rating: 4.7
    },
    {
      id: 3,
      title: 'Es Teh Manis',
      category: 'drinks',
      price: 'Rp 10.000',
      image: 'https://images.unsplash.com/photo-1499638673689-79a0b5115d87?ixlib=rb-4.0.3',
      description: 'Teh manis segar dengan es batu',
      rating: 4.5
    }
  ];

  const filteredDishes = activeCategory === 'all'
    ? dishes
    : dishes.filter(dish => dish.category === activeCategory);

  return (
    <div className="menu-page">
      <div className="menu-section">
        <div className="text-center mb-5">
          <h2 className="hero-title text-gradient">Menu Restoran</h2>
        </div>

        {/* Menu Categories */}
        <div className="category-filter">
          {categories.map(category => (
            <button
              key={category.id}
              className={`category-btn ${activeCategory === category.id ? 'active' : ''}`}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Dish Cards */}
        <div className="row">
          {filteredMenus.map(menu => (
            <div className="col-md-4" key={menu.id}>
              <div className="dish-card">
                <div className="dish-image-container loading" style={{height: '80px', maxHeight: '80px', minHeight: '80px'}}>
                  <img
                    src={menu.image || 'https://via.placeholder.com/400x300/f5f5f5/999999?text=Tidak+Ada+Gambar'}
                    alt={menu.nama}
                    className={`dish-image ${!menu.image ? 'error' : ''}`}
                    style={{height: '100%', maxHeight: '80px', objectFit: 'cover'}}
                    onLoad={(e) => {
                      e.target.parentNode.classList.remove('loading');
                    }}
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = 'https://via.placeholder.com/400x300/f5f5f5/999999?text=Gambar+Tidak+Tersedia';
                      e.target.classList.add('error');
                      e.target.parentNode.classList.remove('loading');
                    }}
                  />
                  {menu.badge && (
                    <div className="dish-badge">{menu.badge}</div>
                  )}
                </div>
                <div className="dish-content">
                  <h3 className="dish-title">{menu.nama}</h3>
                  <p className="dish-description">{menu.deskripsi}</p>
                  <div className="dish-meta">
                    <div className="dish-price">{menu.harga}</div>
                    <div className="dish-rating">
                      <div className="dish-rating-stars">★★★★★</div>
                      <span>{menu.rating}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MenuPage;
