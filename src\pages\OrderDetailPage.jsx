import React, { useState, useEffect } from 'react';
import { Card, Table, Badge, Button, Alert, Form, Row, Col } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

const OrderDetailPage = () => {
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);

      // Sample data untuk order detail koki
      const sampleOrders = [
        {
          id: 1,
          order_number: 'ORD-001',
          customer_name: '<PERSON>',
          customer_phone: '081234567890',
          items: [
            { nama_menu: 'Nasi Goreng Spesial', quantity: 2, harga: 25000, total: 50000 },
            { nama_menu: '<PERSON><PERSON><PERSON>', quantity: 2, harga: 8000, total: 16000 }
          ],
          total_amount: 66000,
          status: 'pending',
          notes: 'Pedas sedang',
          created_at: '2024-01-15 10:30:00',
          table_number: 'A1'
        },
        {
          id: 2,
          order_number: 'ORD-002',
          customer_name: 'Jane Smith',
          customer_phone: '081234567891',
          items: [
            { nama_menu: 'Ayam Bakar', quantity: 1, harga: 30000, total: 30000 },
            { nama_menu: 'Nasi Putih', quantity: 1, harga: 5000, total: 5000 }
          ],
          total_amount: 35000,
          status: 'preparing',
          notes: 'Tanpa sambal',
          created_at: '2024-01-15 11:00:00',
          table_number: 'B2'
        },
        {
          id: 3,
          order_number: 'ORD-003',
          customer_name: 'Bob Wilson',
          customer_phone: '081234567892',
          items: [
            { nama_menu: 'Bakso Keju', quantity: 2, harga: 15000, total: 30000 },
            { nama_menu: 'Es Jeruk', quantity: 1, harga: 10000, total: 10000 }
          ],
          total_amount: 40000,
          status: 'ready',
          notes: 'Extra keju',
          created_at: '2024-01-15 11:15:00',
          table_number: 'C3'
        },
        {
          id: 4,
          order_number: 'ORD-004',
          customer_name: 'Alice Brown',
          customer_phone: '081234567893',
          items: [
            { nama_menu: 'Mie Ayam', quantity: 1, harga: 18000, total: 18000 }
          ],
          total_amount: 18000,
          status: 'confirmed',
          notes: '',
          created_at: '2024-01-15 11:30:00',
          table_number: 'D4'
        }
      ];

      setOrders(sampleOrders);
      setError('');
    } catch (error) {
      console.error('Error fetching orders:', error);
      setError('Terjadi kesalahan saat memuat data pesanan');
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      // Update status pesanan
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, status: newStatus }
            : order
        )
      );

      // Di implementasi nyata, kirim request ke API
      console.log(`Order ${orderId} status updated to ${newStatus}`);
    } catch (error) {
      console.error('Error updating order status:', error);
      setError('Gagal mengupdate status pesanan');
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { variant: 'warning', text: 'Menunggu' },
      confirmed: { variant: 'info', text: 'Dikonfirmasi' },
      preparing: { variant: 'primary', text: 'Sedang Dimasak' },
      ready: { variant: 'success', text: 'Siap' },
      delivered: { variant: 'secondary', text: 'Dikirim' },
      cancelled: { variant: 'danger', text: 'Dibatalkan' }
    };

    const config = statusConfig[status] || { variant: 'secondary', text: status };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const getStatusActions = (order) => {
    const { id, status } = order;

    switch (status) {
      case 'pending':
        return (
          <Button
            size="sm"
            variant="info"
            onClick={() => updateOrderStatus(id, 'confirmed')}
          >
            Konfirmasi
          </Button>
        );
      case 'confirmed':
        return (
          <Button
            size="sm"
            variant="primary"
            onClick={() => updateOrderStatus(id, 'preparing')}
          >
            Mulai Masak
          </Button>
        );
      case 'preparing':
        return (
          <Button
            size="sm"
            variant="success"
            onClick={() => updateOrderStatus(id, 'ready')}
          >
            Selesai
          </Button>
        );
      case 'ready':
        return (
          <Badge bg="success">Siap Disajikan</Badge>
        );
      default:
        return null;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const filteredOrders = filterStatus === 'all'
    ? orders
    : orders.filter(order => order.status === filterStatus);

  if (loading) {
    return (
      <div className="text-center mt-5">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Memuat data pesanan...</p>
      </div>
    );
  }

  return (
    <div className="order-detail-page">
      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="hero-title text-gradient">📋 Detail Order</h2>
          <p className="text-muted">Kelola pesanan pelanggan</p>
        </div>
        <Button variant="outline-primary" onClick={fetchOrders}>
          🔄 Refresh
        </Button>
      </div>

      {/* Filter */}
      <Card className="mb-4">
        <Card.Body>
          <Row>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Filter Status</Form.Label>
                <Form.Select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <option value="all">Semua Status</option>
                  <option value="pending">Menunggu</option>
                  <option value="confirmed">Dikonfirmasi</option>
                  <option value="preparing">Sedang Dimasak</option>
                  <option value="ready">Siap</option>
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Orders Table */}
      <Card>
        <Card.Header className="d-flex justify-content-between align-items-center">
          <h5 className="mb-0">Daftar Pesanan</h5>
          <Badge bg="secondary">{filteredOrders.length} pesanan</Badge>
        </Card.Header>
        <Card.Body>
          {filteredOrders.length === 0 ? (
            <div className="text-center py-4">
              <div className="display-1">🍽️</div>
              <h5 className="text-muted">Tidak ada pesanan</h5>
              <p className="text-muted">
                {filterStatus === 'all'
                  ? 'Belum ada pesanan yang masuk.'
                  : `Tidak ada pesanan dengan status "${filterStatus}".`
                }
              </p>
            </div>
          ) : (
            <div className="table-responsive">
              <Table striped bordered hover>
                <thead className="table-light">
                  <tr>
                    <th>No. Order</th>
                    <th>Pelanggan</th>
                    <th>Meja</th>
                    <th>Menu</th>
                    <th>Total</th>
                    <th>Status</th>
                    <th>Catatan</th>
                    <th>Waktu</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredOrders.map((order) => (
                    <tr key={order.id}>
                      <td>
                        <Badge bg="light" text="dark">{order.order_number}</Badge>
                      </td>
                      <td>
                        <div>
                          <strong>{order.customer_name}</strong>
                          <br />
                          <small className="text-muted">{order.customer_phone}</small>
                        </div>
                      </td>
                      <td>
                        <Badge bg="info">{order.table_number}</Badge>
                      </td>
                      <td>
                        <div>
                          {order.items.map((item, index) => (
                            <div key={index} className="mb-1">
                              <small>
                                <strong>{item.nama_menu}</strong> x{item.quantity}
                                <br />
                                <span className="text-muted">{formatCurrency(item.harga)} each</span>
                              </small>
                            </div>
                          ))}
                        </div>
                      </td>
                      <td>
                        <strong>{formatCurrency(order.total_amount)}</strong>
                      </td>
                      <td>{getStatusBadge(order.status)}</td>
                      <td>
                        <small className="text-muted">
                          {order.notes || '-'}
                        </small>
                      </td>
                      <td>
                        <small className="text-muted">
                          {new Date(order.created_at).toLocaleString('id-ID')}
                        </small>
                      </td>
                      <td>
                        {getStatusActions(order)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Quick Stats */}
      <Row className="mt-4">
        <Col md={3}>
          <Card className="text-center border-warning">
            <Card.Body>
              <div className="display-4 text-warning">⏳</div>
              <h3 className="text-warning">
                {orders.filter(o => o.status === 'pending').length}
              </h3>
              <p className="text-muted mb-0">Menunggu</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-primary">
            <Card.Body>
              <div className="display-4 text-primary">🍳</div>
              <h3 className="text-primary">
                {orders.filter(o => o.status === 'preparing').length}
              </h3>
              <p className="text-muted mb-0">Sedang Dimasak</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-success">
            <Card.Body>
              <div className="display-4 text-success">✅</div>
              <h3 className="text-success">
                {orders.filter(o => o.status === 'ready').length}
              </h3>
              <p className="text-muted mb-0">Siap Disajikan</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-info">
            <Card.Body>
              <div className="display-4 text-info">📊</div>
              <h3 className="text-info">{orders.length}</h3>
              <p className="text-muted mb-0">Total Pesanan</p>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default OrderDetailPage;
