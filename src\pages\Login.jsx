import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/api';

const Login = () => {
  const [form, setForm] = useState({ username: '', password: '' });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      const res = await authService.login(form);
      if (res && res.user) {
        navigate('/');
      } else {
        setError(res.error || 'Login gagal.');
      }
    } catch (err) {
      setError('<PERSON><PERSON><PERSON><PERSON> k<PERSON>.');
    }
    setLoading(false);
  };

  return (
    <div className="container" style={{ maxWidth: 400, margin: '40px auto' }}>
      <h2 className="mb-4 text-center">Login</h2>
      <form onSubmit={handleSubmit}>
        <div className="mb-3">
          <label htmlFor="username" className="form-label">Username</label>
          <input type="text" className="form-control" id="username" name="username" value={form.username} onChange={handleChange} required />
        </div>
        <div className="mb-3">
          <label htmlFor="password" className="form-label">Password</label>
          <input type="password" className="form-control" id="password" name="password" value={form.password} onChange={handleChange} required />
        </div>
        {error && <div className="alert alert-danger py-2">{error}</div>}
        <button type="submit" className="btn btn-primary w-100" disabled={loading}>{loading ? 'Memproses...' : 'Login'}</button>
      </form>
      <div className="mt-3 text-center">
        Belum punya akun? <a href="/register">Daftar</a>
      </div>
    </div>
  );
};

export default Login;