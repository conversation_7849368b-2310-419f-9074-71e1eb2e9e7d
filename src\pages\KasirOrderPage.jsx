import React, { useState, useEffect } from 'react';
import { Card, Table, Badge, Button, Alert, Form, Row, Col, Modal } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

const KasirOrderPage = () => {
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showModal, setShowModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      
      // Sample data untuk kasir - menampilkan pesanan yang perlu diproses
      const sampleOrders = [
        {
          id: '#1',
          menu: '<PERSON>s <PERSON>',
          qty: 1,
          harga: 'Rp 18.000',
          pelanggan: 'Guest User',
          status: 'pending',
          waktu: '2024/01/15 14:45'
        },
        {
          id: '#2',
          menu: 'Ayam Bakar Madu',
          qty: 2,
          harga: 'Rp 50.000',
          pelanggan: 'John Doe',
          status: 'confirmed',
          waktu: '2024/01/15 14:48'
        },
        {
          id: '#3',
          menu: 'Nasi Goreng Spesial',
          qty: 1,
          harga: 'Rp 30.000',
          pelanggan: 'Jane Smith',
          status: 'preparing',
          waktu: '2024/01/15 14:52'
        },
        {
          id: '#4',
          menu: 'Es Teh Manis',
          qty: 2,
          harga: 'Rp 24.000',
          pelanggan: 'Bob Wilson',
          status: 'ready',
          waktu: '2024/01/15 14:55'
        },
        {
          id: '#5',
          menu: 'Ayam Bakar Madu',
          qty: 1,
          harga: 'Rp 35.000',
          pelanggan: 'Alice Brown',
          status: 'delivered',
          waktu: '2024/01/15 15:02'
        },
        {
          id: '#6',
          menu: 'Sate Ayam',
          qty: 1,
          harga: 'Rp 35.000',
          pelanggan: 'Alice Brown',
          status: 'being_delivered',
          waktu: '2024/01/15 15:14'
        }
      ];
      
      setOrders(sampleOrders);
      setError('');
    } catch (error) {
      console.error('Error fetching orders:', error);
      setError('Terjadi kesalahan saat memuat data pesanan');
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      // Update status pesanan
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === orderId 
            ? { ...order, status: newStatus }
            : order
        )
      );
      
      console.log(`Order ${orderId} status updated to ${newStatus}`);
    } catch (error) {
      console.error('Error updating order status:', error);
      setError('Gagal mengupdate status pesanan');
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { variant: 'warning', text: 'Pending' },
      confirmed: { variant: 'info', text: 'Confirmed' },
      preparing: { variant: 'primary', text: 'Preparing' },
      ready: { variant: 'success', text: 'Ready' },
      delivered: { variant: 'secondary', text: 'Delivered' },
      being_delivered: { variant: 'info', text: 'Being Delivered' },
      cancelled: { variant: 'danger', text: 'Cancelled' }
    };

    const config = statusConfig[status] || { variant: 'secondary', text: status };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const getStatusActions = (order) => {
    const { id, status } = order;
    
    switch (status) {
      case 'pending':
        return (
          <Button 
            size="sm" 
            variant="info" 
            onClick={() => updateOrderStatus(id, 'confirmed')}
          >
            Konfirmasi
          </Button>
        );
      case 'ready':
        return (
          <Button 
            size="sm" 
            variant="success" 
            onClick={() => updateOrderStatus(id, 'delivered')}
          >
            Selesai
          </Button>
        );
      default:
        return (
          <Button 
            size="sm" 
            variant="outline-primary" 
            onClick={() => handleViewDetail(order)}
          >
            Detail
          </Button>
        );
    }
  };

  const handleViewDetail = (order) => {
    setSelectedOrder(order);
    setShowModal(true);
  };

  const filteredOrders = filterStatus === 'all' 
    ? orders 
    : orders.filter(order => order.status === filterStatus);

  if (loading) {
    return (
      <div className="text-center mt-5">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Memuat data pesanan...</p>
      </div>
    );
  }

  return (
    <div className="kasir-order-page">
      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="hero-title text-gradient">📝 Kelola Pesanan</h2>
          <p className="text-muted">Kelola dan proses pesanan pelanggan</p>
        </div>
        <Button variant="outline-primary" onClick={fetchOrders}>
          🔄 Refresh
        </Button>
      </div>

      {/* Filter */}
      <Card className="mb-4">
        <Card.Body>
          <Row>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Filter Status</Form.Label>
                <Form.Select 
                  value={filterStatus} 
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <option value="all">Semua Status</option>
                  <option value="pending">Pending</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="preparing">Preparing</option>
                  <option value="ready">Ready</option>
                  <option value="delivered">Delivered</option>
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Orders Table */}
      <Card>
        <Card.Header className="d-flex justify-content-between align-items-center">
          <h5 className="mb-0">Daftar Pesanan</h5>
          <Badge bg="secondary">{filteredOrders.length} pesanan</Badge>
        </Card.Header>
        <Card.Body>
          {filteredOrders.length === 0 ? (
            <div className="text-center py-4">
              <div className="display-1">📋</div>
              <h5 className="text-muted">Tidak ada pesanan</h5>
              <p className="text-muted">
                {filterStatus === 'all' 
                  ? 'Belum ada pesanan yang masuk.' 
                  : `Tidak ada pesanan dengan status "${filterStatus}".`
                }
              </p>
            </div>
          ) : (
            <div className="table-responsive">
              <Table striped bordered hover>
                <thead className="table-light">
                  <tr>
                    <th>ID</th>
                    <th>Menu</th>
                    <th>Qty</th>
                    <th>Harga</th>
                    <th>Pelanggan</th>
                    <th>Status</th>
                    <th>Waktu</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredOrders.map((order) => (
                    <tr key={order.id}>
                      <td>
                        <Badge bg="light" text="dark">{order.id}</Badge>
                      </td>
                      <td>
                        <strong>{order.menu}</strong>
                      </td>
                      <td>
                        <Badge bg="info">{order.qty}</Badge>
                      </td>
                      <td>
                        <strong className="text-success">{order.harga}</strong>
                      </td>
                      <td>{order.pelanggan}</td>
                      <td>{getStatusBadge(order.status)}</td>
                      <td>
                        <small className="text-muted">{order.waktu}</small>
                      </td>
                      <td>
                        {getStatusActions(order)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Detail Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Detail Pesanan {selectedOrder?.id}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedOrder && (
            <div>
              <Row>
                <Col md={6}>
                  <p><strong>Menu:</strong> {selectedOrder.menu}</p>
                  <p><strong>Quantity:</strong> {selectedOrder.qty}</p>
                  <p><strong>Harga:</strong> {selectedOrder.harga}</p>
                </Col>
                <Col md={6}>
                  <p><strong>Pelanggan:</strong> {selectedOrder.pelanggan}</p>
                  <p><strong>Status:</strong> {getStatusBadge(selectedOrder.status)}</p>
                  <p><strong>Waktu:</strong> {selectedOrder.waktu}</p>
                </Col>
              </Row>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Tutup
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default KasirOrderPage;
