/* Modern CSS Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Prismatic Color Palette */
  --primary-color: #667eea;
  --primary-light: #764ba2;
  --primary-dark: #4c63d2;
  --secondary-color: #f093fb;
  --accent-color: #4facfe;
  --tertiary-color: #43e97b;
  --quaternary-color: #38f9d7;
  --background-light: #fdfbfb;
  --background-white: #ffffff;
  --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --text-dark: #2d3748;
  --text-muted: #718096;
  --border-color: #e2e8f0;

  /* Prismatic Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-tertiary: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-quaternary: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --gradient-rainbow: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #4facfe 75%, #43e97b 100%);
  --gradient-soft: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  --gradient-ocean: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  --gradient-sunset: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);

  /* Enhanced Shadows with prismatic tint */
  --shadow-sm: 0 2px 8px rgba(102, 126, 234, 0.08);
  --shadow-md: 0 4px 16px rgba(102, 126, 234, 0.12);
  --shadow-lg: 0 8px 32px rgba(102, 126, 234, 0.16);
  --shadow-xl: 0 16px 64px rgba(102, 126, 234, 0.20);
  --shadow-glow: 0 0 30px rgba(102, 126, 234, 0.4);
  --shadow-rainbow: 0 8px 32px rgba(102, 126, 234, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);

  /* Transitions and Animations */
  --transition-speed-fast: 0.2s;
  --transition-speed: 0.3s;
  --transition-speed-slow: 0.5s;

  /* Border Radius */
  --border-radius: 12px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;
  --border-radius-full: 9999px;

  /* Easing Functions */
  --cubic-bezier-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
  --cubic-bezier-smooth: cubic-bezier(0.65, 0, 0.35, 1);
  --cubic-bezier-elastic: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --cubic-bezier-gentle: cubic-bezier(0.4, 0, 0.2, 1);
  --cubic-bezier-accelerate: cubic-bezier(0.4, 0, 1, 1);
  --cubic-bezier-decelerate: cubic-bezier(0, 0, 0.2, 1);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  color: var(--text-dark);
  background: var(--gradient-ocean);
  line-height: 1.6;
  overflow-x: hidden;
}

#root {
  max-width: 100%;
  margin: 0;
  padding: 0;
}

.App {
  min-height: 100vh;
  background: var(--gradient-ocean);
  position: relative;
  transition: all var(--transition-speed) ease;
}

.App::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(10px) rotate(-1deg); }
}

/* Sidebar Styles */
.sidebar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-rainbow);
  transition: all 0.3s ease;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  width: 280px;
  transform: translateX(0);
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1.5rem;
  overflow-y: auto;
}

.sidebar-top {
  flex-grow: 1;
}

.sidebar-bottom {
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  transform: translateX(-100%);
}

.sidebar-toggle {
  position: fixed;
  left: 280px;
  top: 20px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0 8px 8px 0;
  padding: 10px;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle.collapsed {
  left: 0;
}

.sidebar-toggle:hover {
  background: var(--primary-light);
  transform: translateX(5px);
}

.main-content {
  margin-left: 280px;
  transition: all 0.3s ease;
}

.main-content.expanded {
  margin-left: 0;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-rainbow);
  opacity: 0.03;
  pointer-events: none;
}

.sidebar:hover {
  box-shadow: var(--shadow-glow);
  transform: translateX(2px);
}

.sidebar-sticky {
  position: sticky;
  top: 1.5rem;
}

/* Main Content Styles */
.main-content {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  min-height: 100vh;
  border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
  box-shadow: var(--shadow-rainbow);
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-sunset);
  opacity: 0.02;
  pointer-events: none;
}

.main-content:hover {
  box-shadow: var(--shadow-glow);
}

/* Navigation Styles */
.nav-container {
  width: 100%;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-rainbow);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  overflow: hidden;
  transition: all var(--transition-speed) ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.nav-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-rainbow);
  opacity: 0.05;
  pointer-events: none;
}

.nav-container:hover {
  box-shadow: var(--shadow-glow);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-2px);
}

.nav-header {
  background: var(--gradient-primary);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  text-align: center;
  padding: 1.2rem;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.nav-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-container:hover .nav-header::after {
  left: 100%;
}

.nav-body {
  padding: 0;
}

.nav-link {
  color: var(--text-dark) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem 1.5rem !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: block;
  text-decoration: none;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0 4px 4px 0;
}

.nav-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-tertiary);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.nav-link:hover {
  color: white !important;
  padding-left: 2rem !important;
  text-decoration: none;
  transform: translateX(4px);
}

.nav-link:hover::before {
  opacity: 1;
  width: 6px;
}

.nav-link:hover::after {
  opacity: 0.8;
}

.nav-link.active {
  background: var(--gradient-primary);
  color: white !important;
  font-weight: 600;
  border-left: 4px solid var(--tertiary-color);
  transform: translateX(2px);
}

.nav-link.active::before {
  opacity: 1;
  background: var(--gradient-quaternary);
}

/* Additional Sidebar Enhancements */
.nav-icon {
  font-size: 1.2rem;
  width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  transition: all 0.3s ease;
  opacity: 0.8;
}

.nav-link:hover .nav-icon {
  opacity: 1;
  transform: scale(1.1);
}

.nav-label {
  font-weight: 500;
  transition: all 0.3s ease;
}

/* Navigation Container Enhancements */
.nav-container {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  margin-bottom: 1rem;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.nav-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  padding: 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhance sidebar toggle button */
.sidebar-toggle {
  width: 32px;
  height: 32px;
  font-size: 1.2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.9;
}

.sidebar-toggle:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Brand gradient enhancement */
.brand-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
  font-size: 1.5rem;
  letter-spacing: -0.5px;
}

/* Top Navigation Bar */
.top-nav {
  position: fixed;
  top: 0;
  right: 0;
  padding: 1rem 2rem;
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.profile-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 0.5rem 1rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.profile-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-details {
  display: flex;
  flex-direction: column;
}

.profile-name {
  font-weight: 600;
  color: var(--text-dark);
}

.profile-role {
  color: var(--text-muted);
  font-size: 0.8rem;
}

.profile-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  padding-left: 1rem;
  margin-left: 0.5rem;
}

.auth-buttons {
  background: rgba(255, 255, 255, 0.95);
  padding: 0.5rem 1rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* Profile styles in sidebar */
.nav-profile {
  margin-top: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.profile-card {
  padding: 1rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.profile-avatar {
  width: 45px;
  height: 45px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.profile-details {
  flex: 1;
  min-width: 0;
}

.profile-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-dark);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.profile-role {
  font-size: 0.8rem;
  color: var(--text-muted);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.profile-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.auth-buttons-container {
  padding: 1rem;
}

/* User Profile Styles */
.user-profile {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.profile-card {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.profile-avatar {
  width: 45px;
  height: 45px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.profile-details {
  flex: 1;
  min-width: 0;
}

.profile-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-dark);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.profile-role {
  font-size: 0.8rem;
  color: var(--text-muted);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.profile-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.5rem;
}

@media (max-width: 768px) {
  .top-nav {
    padding: 0.5rem 1rem;
  }

  .profile-menu {
    padding: 0.25rem 0.5rem;
  }

  .profile-icon {
    width: 32px;
    height: 32px;
    font-size: 1.2rem;
  }

  .profile-actions {
    flex-direction: column;
    padding-left: 0.5rem;
    gap: 0.25rem;
  }

  .sidebar {
    width: 260px;
  }

  .sidebar-toggle {
    width: 28px;
    height: 28px;
    font-size: 1rem;
  }

  .main-content {
    margin-left: 0;
  }

  .sidebar:not(.collapsed) + .main-content {
    margin-left: 260px;
  }
}

/* Koki Dashboard Specific Styles */
.top-header {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #dee2e6 !important;
}

.menu-header {
  background-color: #f8f9fa !important;
  border: 1px solid #dee2e6;
}

.sidebar-link {
  transition: background-color 0.2s ease;
}

.sidebar-link:hover {
  background-color: #f0f7ff !important;
  color: #2b6cb0 !important;
}

.sidebar-link.active {
  background-color: #ebf8ff !important;
  color: #2c5282 !important;
  font-weight: 500;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

.table-bordered {
  border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
  border: 1px solid #dee2e6;
}
