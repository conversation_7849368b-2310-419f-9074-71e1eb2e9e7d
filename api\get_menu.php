<?php
require_once 'config.php';
header('Content-Type: application/json');

$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    echo json_encode(['success' => false, 'message' => 'Koneksi database gagal']);
    exit;
}

$sql = "SELECT * FROM menus ORDER BY menu ASC";
$result = $conn->query($sql);
$menus = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        // Process image URL
        if (!empty($row['gambar'])) {
            // If it's already a full URL (starts with http), keep it as is
            if (strpos($row['gambar'], 'http') === 0) {
                $row['gambar_url'] = $row['gambar'];
            } else {
                // If it's a filename, construct the full URL
                $row['gambar_url'] = 'http://' . $_SERVER['HTTP_HOST'] . '/project-react-resto/uploads/' . $row['gambar'];
            }
        } else {
            $row['gambar_url'] = null;
        }

        // Add some default values for frontend compatibility
        $row['image'] = $row['gambar_url']; // For compatibility with frontend
        $row['name'] = $row['menu']; // For compatibility with frontend
        $row['description'] = $row['deskripsi']; // For compatibility with frontend
        $row['price'] = floatval($row['harga']); // Ensure price is numeric
        $row['category'] = 'Main Course'; // Default category
        $row['rating'] = 4.5; // Default rating
        $row['preparationTime'] = '15-20 min'; // Default prep time
        $row['calories'] = 350; // Default calories
        $row['isNew'] = false; // Default new status
        $row['isPopular'] = false; // Default popular status
        $row['ingredients'] = ['Fresh ingredients']; // Default ingredients
        $row['allergens'] = []; // Default allergens

        $menus[] = $row;
    }
    echo json_encode(['success' => true, 'data' => $menus]);
} else {
    echo json_encode(['success' => true, 'data' => []]);
}
$conn->close();