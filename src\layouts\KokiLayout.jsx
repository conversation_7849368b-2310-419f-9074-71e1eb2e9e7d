import React, { useState } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { Container, Row, Col, Button } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

/**
 * Layout untuk role Koki
 * Hanya menampilkan Order Detail di sidebar
 */
const KokiLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Menu items untuk koki - hanya order detail
  const kokiMenuItems = [
    { path: '.', label: 'Dashboard Koki', icon: '👨‍🍳' },
    { path: 'order-detail', label: 'Order Detail', icon: '📋' }
  ];

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="App">
      {/* Sidebar Toggle Button */}
      <button
        className={`sidebar-toggle ${isSidebarCollapsed ? 'collapsed' : ''}`}
        onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
        aria-label="Toggle Sidebar"
      >
        {isSidebarCollapsed ? '→' : '←'}
      </button>

      {/* Sidebar */}
      <div className={`sidebar ${isSidebarCollapsed ? 'collapsed' : ''}`}>
        <div className="sidebar-content">
          {/* Top Section */}
          <div className="sidebar-top">
            <h3 className="mb-4 text-center">
              <Link to="/koki" style={{ textDecoration: 'none' }}>
                <span className="brand-gradient">👨‍🍳 Koki Panel</span>
              </Link>
            </h3>

            {/* Navigation Menu */}
            <div className="sidebar-nav">
              {kokiMenuItems.map((item) => {
                const isActive = location.pathname === `/koki/${item.path}` || 
                                (item.path === '.' && location.pathname === '/koki');
                
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`sidebar-link ${isActive ? 'active' : ''}`}
                  >
                    <span className="sidebar-icon">{item.icon}</span>
                    <span className="sidebar-text">{item.label}</span>
                  </Link>
                );
              })}
            </div>
          </div>

          {/* Bottom Section - User Info */}
          <div className="sidebar-bottom">
            <div className="user-info">
              <div className="user-avatar">
                <span className="avatar-icon">👨‍🍳</span>
              </div>
              <div className="user-details">
                <div className="user-name">{user?.name}</div>
                <div className="user-role">Koki</div>
              </div>
              <div className="user-actions">
                <Button
                  variant="outline-danger"
                  size="sm"
                  className="w-100"
                  onClick={handleLogout}
                >
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className={`main-content ${isSidebarCollapsed ? 'expanded' : ''}`}>
        <Container fluid className="p-4">
          <Outlet />
        </Container>
      </div>
    </div>
  );
};

export default KokiLayout;
